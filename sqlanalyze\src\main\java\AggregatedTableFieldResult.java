import java.util.*;
import java.io.*;
import java.nio.file.*;
import java.nio.charset.StandardCharsets;

/**
 * 聚合的表字段分析结果
 * 按表和过滤字段组合聚合，关联字段和所有字段做集合合并
 */
public class AggregatedTableFieldResult {
    
    /**
     * 聚合键：表名 + 过滤字段组合
     */
    public static class AggregationKey {
        private final String tableName;
        private final String filterFieldsCombination; // 用逗号分隔的过滤字段组合
        
        public AggregationKey(String tableName, List<String> filterFields) {
            this.tableName = tableName.toUpperCase();
            this.filterFieldsCombination = String.join(",", filterFields);
        }
        
        public String getTableName() { return tableName; }
        public String getFilterFieldsCombination() { return filterFieldsCombination; }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            AggregationKey that = (AggregationKey) o;
            return Objects.equals(tableName, that.tableName) &&
                   Objects.equals(filterFieldsCombination, that.filterFieldsCombination);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(tableName, filterFieldsCombination);
        }
        
        @Override
        public String toString() {
            return tableName + "[" + filterFieldsCombination + "]";
        }
    }
    
    /**
     * 聚合后的表字段信息
     */
    public static class AggregatedTableInfo {
        private String tableName;
        private String filterFieldsCombination;
        private Set<String> joinFields = new HashSet<>();
        private Set<String> allFields = new HashSet<>();
        private int sqlCount = 0; // 聚合的SQL数量
        
        public AggregatedTableInfo(String tableName, String filterFieldsCombination) {
            this.tableName = tableName;
            this.filterFieldsCombination = filterFieldsCombination;
        }
        
        public void addJoinFields(Set<String> fields) {
            joinFields.addAll(fields);
        }
        
        public void addAllFields(Set<String> fields) {
            allFields.addAll(fields);
        }
        
        public void incrementSqlCount() {
            sqlCount++;
        }
        
        // Getters
        public String getTableName() { return tableName; }
        public String getFilterFieldsCombination() { return filterFieldsCombination; }
        public Set<String> getJoinFields() { return joinFields; }
        public Set<String> getAllFields() { return allFields; }
        public int getSqlCount() { return sqlCount; }
        
        @Override
        public String toString() {
            return String.format("%s：关联字段%s,过滤字段[%s],所有字段%s (聚合%d个SQL)", 
                               tableName, joinFields, filterFieldsCombination, allFields, sqlCount);
        }
    }
    
    // 聚合结果存储
    private Map<AggregationKey, AggregatedTableInfo> aggregatedResults = new HashMap<>();
    
    /**
     * 从SqlAnalysisResult创建聚合结果
     */
    public static AggregatedTableFieldResult fromSqlAnalysisResult(SqlAnalysisResult sqlResult) {
        AggregatedTableFieldResult aggregated = new AggregatedTableFieldResult();

        for (Map.Entry<SqlAnalysisResult.TableFilterKey, SqlAnalysisResult.GroupStatistics> entry :
             sqlResult.getGroupedResults().entrySet()) {

            SqlAnalysisResult.TableFilterKey key = entry.getKey();
            SqlAnalysisResult.GroupStatistics stats = entry.getValue();

            String tableName = key.getTableName().toUpperCase();

            // 获取过滤字段列表（已排序）
            List<String> sortedFilterFields = new ArrayList<>(key.getFilterFields());
            Collections.sort(sortedFilterFields);

            // 创建聚合键
            AggregationKey aggKey = new AggregationKey(tableName, sortedFilterFields);

            // 获取或创建聚合信息
            AggregatedTableInfo aggregatedInfo = aggregated.aggregatedResults.computeIfAbsent(aggKey,
                k -> new AggregatedTableInfo(tableName, k.getFilterFieldsCombination()));

            // 合并字段信息
            aggregatedInfo.addJoinFields(stats.getJoinFields());
            aggregatedInfo.addAllFields(stats.getAllFields());
            // 设置SQL数量为统计次数
            for (int i = 0; i < stats.getCount(); i++) {
                aggregatedInfo.incrementSqlCount();
            }
        }

        return aggregated;
    }
    
    /**
     * 打印聚合结果
     */
    public void printResults() {
        System.out.println("=== 聚合后的表字段分析结果 ===");
        System.out.println("按表和过滤字段组合聚合，关联字段和所有字段做集合合并");
        System.out.println("过滤字段按出现次数排序（出现次数多的排前面）");
        System.out.println("总共 " + aggregatedResults.size() + " 个不同的表-过滤字段组合");
        System.out.println();

        // 输出CSV格式
        printCsvResults();

        System.out.println();
        System.out.println("=== 聚合统计 ===");
        Map<String, Integer> tableStats = new HashMap<>();
        for (AggregatedTableInfo info : aggregatedResults.values()) {
            tableStats.put(info.getTableName(),
                tableStats.getOrDefault(info.getTableName(), 0) + info.getSqlCount());
        }

        for (Map.Entry<String, Integer> entry : tableStats.entrySet()) {
            System.out.println(String.format("表 %s: 总共涉及 %d 个SQL",
                             entry.getKey(), entry.getValue()));
        }
    }

    /**
     * 打印CSV格式的结果
     */
    public void printCsvResults() {
        System.out.println("=== CSV格式输出 ===");

        // 输出CSV头部
        System.out.println("表名,过滤字段组,关联字段组,所有字段组");

        // 按表名排序输出
        List<Map.Entry<AggregationKey, AggregatedTableInfo>> sortedEntries = new ArrayList<>(aggregatedResults.entrySet());
        sortedEntries.sort((e1, e2) -> {
            int tableCompare = e1.getKey().getTableName().compareTo(e2.getKey().getTableName());
            if (tableCompare != 0) return tableCompare;
            return e1.getKey().getFilterFieldsCombination().compareTo(e2.getKey().getFilterFieldsCombination());
        });

        // 输出每一行数据
        for (Map.Entry<AggregationKey, AggregatedTableInfo> entry : sortedEntries) {
            AggregatedTableInfo info = entry.getValue();

            String tableName = info.getTableName();
            String filterFields = info.getFilterFieldsCombination();
            String joinFields = formatFieldsForCsv(info.getJoinFields());
            String allFields = formatFieldsForCsv(info.getAllFields());

            // 输出CSV行，如果字段包含逗号则用双引号包围
            System.out.println(String.format("%s,\"%s\",\"%s\",\"%s\"",
                              tableName, filterFields, joinFields, allFields));
        }
    }

    /**
     * 格式化字段集合为CSV友好的字符串
     */
    private String formatFieldsForCsv(Set<String> fields) {
        if (fields.isEmpty()) {
            return "";
        }

        // 将Set转换为排序的List
        List<String> sortedFields = new ArrayList<>(fields);
        Collections.sort(sortedFields);

        // 用逗号连接
        return String.join(",", sortedFields);
    }
    
    /**
     * 保存CSV结果到文件
     */
    public void saveCsvToFile(String fileName) {
        try {
            List<String> lines = new ArrayList<>();

            // 添加CSV头部
            lines.add("表名,过滤字段组,关联字段组,所有字段组");

            // 按表名排序
            List<Map.Entry<AggregationKey, AggregatedTableInfo>> sortedEntries = new ArrayList<>(aggregatedResults.entrySet());
            sortedEntries.sort((e1, e2) -> {
                int tableCompare = e1.getKey().getTableName().compareTo(e2.getKey().getTableName());
                if (tableCompare != 0) return tableCompare;
                return e1.getKey().getFilterFieldsCombination().compareTo(e2.getKey().getFilterFieldsCombination());
            });

            // 添加数据行
            for (Map.Entry<AggregationKey, AggregatedTableInfo> entry : sortedEntries) {
                AggregatedTableInfo info = entry.getValue();

                String tableName = info.getTableName();
                String filterFields = info.getFilterFieldsCombination();
                String joinFields = formatFieldsForCsv(info.getJoinFields());
                String allFields = formatFieldsForCsv(info.getAllFields());

                lines.add(String.format("%s,\"%s\",\"%s\",\"%s\"",
                                      tableName, filterFields, joinFields, allFields));
            }

            // 写入文件
            Files.write(Paths.get(fileName), lines, StandardCharsets.UTF_8);
            System.out.println("CSV结果已保存到文件: " + fileName);

        } catch (IOException e) {
            System.err.println("保存CSV文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取聚合结果
     */
    public Map<AggregationKey, AggregatedTableInfo> getAggregatedResults() {
        return aggregatedResults;
    }
}
