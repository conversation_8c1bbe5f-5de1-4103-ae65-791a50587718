import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 存储SQL分析结果的数据结构
 * 按表+过滤字段组合进行分组，统计出现次数并合并关联字段
 */
public class SqlAnalysisResult {

    /**
     * 表+过滤字段组合的分组键
     */
    public static class TableFilterKey {
        private final String tableName;
        private final Set<String> filterFields;

        public TableFilterKey(String tableName, Set<String> filterFields) {
            this.tableName = tableName;
            this.filterFields = new HashSet<>(filterFields);
        }

        public String getTableName() { return tableName; }
        public Set<String> getFilterFields() { return filterFields; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TableFilterKey that = (TableFilterKey) o;
            return Objects.equals(tableName, that.tableName) &&
                   Objects.equals(filterFields, that.filterFields);
        }

        @Override
        public int hashCode() {
            return Objects.hash(tableName, filterFields);
        }

        @Override
        public String toString() {
            return String.format("TableFilterKey{table=%s, filterFields=%s}",
                               tableName, filterFields);
        }
    }

    /**
     * 分组统计信息
     */
    public static class GroupStatistics {
        private final String tableName;
        private final Set<String> filterFields;
        private final Set<String> joinFields;
        private final Set<String> allFields;
        private int count;
        private final List<String> sqlFragments;

        public GroupStatistics(String tableName, Set<String> filterFields) {
            this.tableName = tableName;
            this.filterFields = new HashSet<>(filterFields);
            this.joinFields = new HashSet<>();
            this.allFields = new HashSet<>();
            this.count = 0;
            this.sqlFragments = new ArrayList<>();

            // 过滤字段也是所有字段的一部分
            this.allFields.addAll(filterFields);
        }

        /**
         * 增加一次出现，并合并关联字段
         */
        public void addOccurrence(Set<String> newJoinFields, String sqlFragment) {
            this.count++;
            this.joinFields.addAll(newJoinFields);
            this.allFields.addAll(newJoinFields);
            if (sqlFragment != null) {
                this.sqlFragments.add(sqlFragment);
            }
        }

        // Getters
        public String getTableName() { return tableName; }
        public Set<String> getFilterFields() { return filterFields; }
        public Set<String> getJoinFields() { return joinFields; }
        public Set<String> getAllFields() { return allFields; }
        public int getCount() { return count; }
        public List<String> getSqlFragments() { return sqlFragments; }

        @Override
        public String toString() {
            List<String> sortedFilterFields = new ArrayList<>(filterFields);
            Collections.sort(sortedFilterFields);
            List<String> sortedJoinFields = new ArrayList<>(joinFields);
            Collections.sort(sortedJoinFields);
            List<String> sortedAllFields = new ArrayList<>(allFields);
            Collections.sort(sortedAllFields);

            return String.format("%s：关联字段%s,过滤字段%s,所有字段%s,出现次数%d",
                               tableName, sortedJoinFields, sortedFilterFields,
                               sortedAllFields, count);
        }
    }

    // 按表+过滤字段组合进行分组统计
    private Map<TableFilterKey, GroupStatistics> groupedResults = new HashMap<>();

    /**
     * 清理字段名：去掉表别名和"."
     * 例如：a.user_id -> user_id, users.name -> name
     */
    private String cleanFieldName(String field) {
        if (field != null && field.contains(".")) {
            return field.substring(field.lastIndexOf(".") + 1);
        }
        return field;
    }

    /**
     * 判断字段是否属于指定的表
     */
    private boolean belongsToTable(String field, String table) {
        // 如果字段包含表前缀，直接匹配
        if (field.contains(".")) {
            String prefix = field.substring(0, field.indexOf("."));
            // 检查前缀是否匹配表名或表别名
            return prefix.equalsIgnoreCase(table) ||
                   isTableAlias(prefix, table);
        }
        // 如果没有前缀，假设属于所有表（这种情况下可能需要更复杂的逻辑）
        return true;
    }

    /**
     * 检查是否为表别名（简单实现，可根据需要扩展）
     */
    private boolean isTableAlias(String alias, String tableName) {
        // 简单的别名匹配逻辑：检查别名是否为表名的首字母或简化形式
        if (alias.length() == 1) {
            return tableName.toLowerCase().startsWith(alias.toLowerCase());
        }
        return false;
    }

    /**
     * 添加PlainSelect的分析结果
     * @param tables 涉及的表
     * @param filterColumns 过滤字段
     * @param joinColumns 关联字段
     * @param sqlFragment SQL片段
     */
    public void addPlainSelectResult(Set<String> tables, Set<String> filterColumns,
                                   Set<String> joinColumns, String sqlFragment) {
        // 清理字段名
        Set<String> cleanedFilterColumns = new HashSet<>();
        for (String field : filterColumns) {
            cleanedFilterColumns.add(cleanFieldName(field));
        }

        Set<String> cleanedJoinColumns = new HashSet<>();
        for (String field : joinColumns) {
            cleanedJoinColumns.add(cleanFieldName(field));
        }

        // 为每个表创建分组
        for (String table : tables) {
            // 只保留属于当前表的过滤字段
            Set<String> tableFilterFields = new HashSet<>();
            for (String filterField : cleanedFilterColumns) {
                if (belongsToTable(filterField, table)) {
                    tableFilterFields.add(filterField);
                }
            }

            // 只保留属于当前表的关联字段
            Set<String> tableJoinFields = new HashSet<>();
            for (String joinField : cleanedJoinColumns) {
                if (belongsToTable(joinField, table)) {
                    tableJoinFields.add(joinField);
                }
            }

            // 创建分组键
            TableFilterKey key = new TableFilterKey(table, tableFilterFields);

            // 获取或创建分组统计
            GroupStatistics stats = groupedResults.computeIfAbsent(key,
                k -> new GroupStatistics(table, tableFilterFields));

            // 添加一次出现
            stats.addOccurrence(tableJoinFields, sqlFragment);
        }
    }

    /**
     * 获取分组统计结果
     */
    public Map<TableFilterKey, GroupStatistics> getGroupedResults() {
        return groupedResults;
    }

    /**
     * 获取所有分组统计信息的列表
     */
    public List<GroupStatistics> getAllGroupStatistics() {
        return new ArrayList<>(groupedResults.values());
    }
    
    /**
     * 打印分析结果
     */
    public void printResults() {
        System.out.println("=== SQL分析结果 ===");
        System.out.println("总共涉及 " + groupedResults.size() + " 个表+过滤字段组合");
        System.out.println();

        // 按表名排序输出
        List<TableFilterKey> sortedKeys = new ArrayList<>(groupedResults.keySet());
        sortedKeys.sort((k1, k2) -> {
            int tableCompare = k1.getTableName().compareTo(k2.getTableName());
            if (tableCompare != 0) return tableCompare;
            return k1.getFilterFields().toString().compareTo(k2.getFilterFields().toString());
        });

        int groupIndex = 1;
        for (TableFilterKey key : sortedKeys) {
            GroupStatistics stats = groupedResults.get(key);

            System.out.println("=== 组合 " + groupIndex + " ===");
            System.out.println(stats.toString());

            // 显示部分SQL片段示例
            List<String> fragments = stats.getSqlFragments();
            int maxShow = Math.min(3, fragments.size()); // 最多显示3个示例
            for (int i = 0; i < maxShow; i++) {
                String fragment = fragments.get(i);
                System.out.println("  SQL示例 " + (i + 1) + ": " +
                                 (fragment.length() > 100 ?
                                  fragment.substring(0, 100) + "..." :
                                  fragment));
            }
            if (fragments.size() > maxShow) {
                System.out.println("  ... 还有 " + (fragments.size() - maxShow) + " 个SQL片段");
            }
            System.out.println();
            groupIndex++;
        }

        System.out.println("=== 统计汇总 ===");
        int totalOccurrences = groupedResults.values().stream()
                                           .mapToInt(GroupStatistics::getCount)
                                           .sum();
        System.out.println("总出现次数: " + totalOccurrences);
        System.out.println("不同组合数: " + groupedResults.size());
    }
}
