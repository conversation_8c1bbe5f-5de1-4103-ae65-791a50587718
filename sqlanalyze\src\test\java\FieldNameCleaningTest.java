import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;

/**
 * 字段名清理功能测试
 * 验证QueryInfo中的字段名是否正确去掉了表别名和"."
 */
public class FieldNameCleaningTest {
    
    public static void main(String[] args) {
        System.out.println("=== 字段名清理功能测试 ===");
        System.out.println("验证QueryInfo中保存的字段名是否已去掉表别名和'.'");
        System.out.println();
        
        try {
            // 测试1：简单的表别名
            testSimpleAlias();
            
            // 测试2：复杂的表别名
            testComplexAlias();
            
            // 测试3：混合情况
            testMixedCase();
            
            System.out.println("🎉 字段名清理功能测试完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中出现错误:");
            e.printStackTrace();
        }
    }
    
    /**
     * 测试简单的表别名
     */
    private static void testSimpleAlias() throws Exception {
        System.out.println("=== 测试1：简单表别名 ===");
        
        String sql = "SELECT u.id, u.name, o.amount " +
                    "FROM users u, orders o " +
                    "WHERE u.id = o.user_id AND u.status = 'active'";
        
        SqlAnalysisResult result = analyzeSql(sql);
        
        System.out.println("原始SQL: " + sql);
        System.out.println("预期关联字段: [id, user_id]");
        System.out.println("预期过滤字段: [status]");
        
        for (SqlAnalysisResult.QueryInfo queryInfo : result.getAllQueries()) {
            System.out.println("实际关联字段: " + queryInfo.getJoinColumns());
            System.out.println("实际过滤字段: " + queryInfo.getFilterColumns());
            
            // 验证字段名是否已清理
            validateFieldsCleaned(queryInfo.getJoinColumns(), "关联字段");
            validateFieldsCleaned(queryInfo.getFilterColumns(), "过滤字段");
        }
        
        System.out.println("✅ 简单表别名测试完成\n");
    }
    
    /**
     * 测试复杂的表别名
     */
    private static void testComplexAlias() throws Exception {
        System.out.println("=== 测试2：复杂表别名 ===");
        
        String sql = "SELECT A.C_SEC_CODE, SEC.C_SEC_VAR_CODE, STOCK.C_DV_ACCOUNT_CODE " +
                    "FROM T_D_AC_TRADE_IVT A " +
                    "LEFT JOIN T_P_SV_SEC_BASE SEC ON A.C_SEC_CODE = SEC.C_SEC_CODE " +
                    "LEFT JOIN T_D_MP_PRE_STOCK STOCK ON A.C_SEC_CODE = STOCK.C_SEC_CODE " +
                    "WHERE A.C_PORT_CODE = 'PORT001' AND A.C_DT_CODE = 'BUY'";
        
        SqlAnalysisResult result = analyzeSql(sql);
        
        System.out.println("原始SQL: " + (sql.length() > 80 ? sql.substring(0, 80) + "..." : sql));
        System.out.println("预期关联字段: [C_SEC_CODE]");
        System.out.println("预期过滤字段: [C_PORT_CODE, C_DT_CODE]");
        
        for (SqlAnalysisResult.QueryInfo queryInfo : result.getAllQueries()) {
            System.out.println("实际关联字段: " + queryInfo.getJoinColumns());
            System.out.println("实际过滤字段: " + queryInfo.getFilterColumns());
            
            // 验证字段名是否已清理
            validateFieldsCleaned(queryInfo.getJoinColumns(), "关联字段");
            validateFieldsCleaned(queryInfo.getFilterColumns(), "过滤字段");
        }
        
        System.out.println("✅ 复杂表别名测试完成\n");
    }
    
    /**
     * 测试混合情况
     */
    private static void testMixedCase() throws Exception {
        System.out.println("=== 测试3：混合情况 ===");
        
        String sql = "SELECT u.name, profiles.title, o.amount " +
                    "FROM users u " +
                    "JOIN profiles ON u.id = profiles.user_id " +
                    "JOIN orders o ON u.id = o.user_id " +
                    "WHERE u.created_date > '2024-01-01' AND status = 'completed'";
        
        SqlAnalysisResult result = analyzeSql(sql);
        
        System.out.println("原始SQL: " + (sql.length() > 80 ? sql.substring(0, 80) + "..." : sql));
        System.out.println("预期关联字段: [id, user_id]");
        System.out.println("预期过滤字段: [created_date, status]");
        
        for (SqlAnalysisResult.QueryInfo queryInfo : result.getAllQueries()) {
            System.out.println("实际关联字段: " + queryInfo.getJoinColumns());
            System.out.println("实际过滤字段: " + queryInfo.getFilterColumns());
            
            // 验证字段名是否已清理
            validateFieldsCleaned(queryInfo.getJoinColumns(), "关联字段");
            validateFieldsCleaned(queryInfo.getFilterColumns(), "过滤字段");
        }
        
        System.out.println("✅ 混合情况测试完成\n");
    }
    
    /**
     * 分析SQL并返回结果
     */
    private static SqlAnalysisResult analyzeSql(String sql) throws Exception {
        Statement statement = CCJSqlParserUtil.parse(sql);
        
        if (statement instanceof Select) {
            Select select = (Select) statement;
            ComprehensiveSqlAnalyzer analyzer = new ComprehensiveSqlAnalyzer();
            select.accept(analyzer, null);
            return analyzer.getAnalysisResult();
        } else {
            throw new Exception("不是SELECT语句");
        }
    }
    
    /**
     * 验证字段名是否已清理（不包含"."）
     */
    private static void validateFieldsCleaned(java.util.Set<String> fields, String fieldType) {
        for (String field : fields) {
            if (field.contains(".")) {
                System.err.println("❌ " + fieldType + "中发现未清理的字段名: " + field);
            } else {
                System.out.println("✅ " + fieldType + "字段名已清理: " + field);
            }
        }
    }
}
