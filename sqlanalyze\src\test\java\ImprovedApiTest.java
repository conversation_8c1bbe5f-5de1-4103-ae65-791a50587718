import java.util.HashSet;
import java.util.Set;

/**
 * 测试改进后的API - 精确的表字段对应关系
 */
public class ImprovedApiTest {
    public static void main(String[] args) {
        System.out.println("=== 测试改进后的表字段对应API ===");
        
        // 创建SqlAnalysisResult实例
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // 测试案例1：T_D_AI_STOCK表的分析
        Set<String> filterColumns1 = new HashSet<>();
        filterColumns1.add("A.C_PORT_CODE");
        filterColumns1.add("A.D_STOCK");
        filterColumns1.add("A.C_MKT_CODE");
        
        Set<String> joinColumns1 = new HashSet<>();
        joinColumns1.add("A.C_DAI_CODE");
        
        String sql1 = "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM T_D_AI_STOCK A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3 AND A.C_MKT_CODE = $4";
        result.addTableResult("T_D_AI_STOCK", filterColumns1, joinColumns1, sql1);
        
        // 测试案例2：T_S_DAI_ITEM表的分析（来自同一个SQL）
        Set<String> filterColumns2 = new HashSet<>(); // 没有过滤字段
        
        Set<String> joinColumns2 = new HashSet<>();
        joinColumns2.add("B.C_DAI_CODE");
        
        result.addTableResult("T_S_DAI_ITEM", filterColumns2, joinColumns2, sql1);
        
        // 测试案例3：相同表+相同过滤字段的另一个查询（应该累计次数）
        Set<String> filterColumns3 = new HashSet<>();
        filterColumns3.add("A.C_PORT_CODE");
        filterColumns3.add("A.D_STOCK");
        filterColumns3.add("A.C_MKT_CODE");
        
        Set<String> joinColumns3 = new HashSet<>();
        joinColumns3.add("A.C_DAI_CODE");
        joinColumns3.add("A.C_EXTRA_FIELD"); // 额外的关联字段，应该被合并
        
        String sql2 = "SELECT A.N_AMOUNT FROM T_D_AI_STOCK A WHERE A.C_DAI_CODE = 'XXX' AND A.C_PORT_CODE = $5 AND A.D_STOCK = $6 AND A.C_MKT_CODE = $7";
        result.addTableResult("T_D_AI_STOCK", filterColumns3, joinColumns3, sql2);
        
        // 测试案例4：不同的过滤字段组合
        Set<String> filterColumns4 = new HashSet<>();
        filterColumns4.add("A.C_STATUS");
        filterColumns4.add("A.N_AMOUNT");
        
        Set<String> joinColumns4 = new HashSet<>();
        joinColumns4.add("A.C_DAI_CODE");
        
        String sql3 = "SELECT * FROM T_D_AI_STOCK A WHERE A.C_STATUS = 'ACTIVE' AND A.N_AMOUNT > 1000";
        result.addTableResult("T_D_AI_STOCK", filterColumns4, joinColumns4, sql3);
        
        // 测试案例5：不同的表
        Set<String> filterColumns5 = new HashSet<>();
        filterColumns5.add("B.C_STATUS");
        
        Set<String> joinColumns5 = new HashSet<>();
        joinColumns5.add("B.C_DAI_CODE");
        joinColumns5.add("B.C_ITEM_CODE");
        
        String sql4 = "SELECT * FROM T_S_DAI_ITEM B WHERE B.C_STATUS = 'ENABLED'";
        result.addTableResult("T_S_DAI_ITEM", filterColumns5, joinColumns5, sql4);
        
        // 打印结果
        result.printResults();
        
        System.out.println("\n=== 验证预期结果 ===");
        System.out.println("预期应该看到：");
        System.out.println("1. T_D_AI_STOCK表有2个不同的过滤字段组合：");
        System.out.println("   - 组合1(C_MKT_CODE,C_PORT_CODE,D_STOCK)出现2次，关联字段合并了C_DAI_CODE和C_EXTRA_FIELD");
        System.out.println("   - 组合2(C_STATUS,N_AMOUNT)出现1次");
        System.out.println("2. T_S_DAI_ITEM表有2个不同的过滤字段组合：");
        System.out.println("   - 组合1(空过滤字段)出现1次，关联字段C_DAI_CODE");
        System.out.println("   - 组合2(C_STATUS)出现1次，关联字段C_DAI_CODE和C_ITEM_CODE");
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("✅ 新的API确保了字段与表的精确对应关系");
        System.out.println("✅ 避免了字段归属判断的不准确性");
        System.out.println("✅ 每个表的过滤字段和关联字段都是明确指定的");
    }
}
