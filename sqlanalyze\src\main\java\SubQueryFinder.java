import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.*;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.statement.select.ParenthesedSelect;

import java.util.*;

/**
 * 子查询查找器
 * 用于在表达式中查找所有的子查询
 */
public class SubQueryFinder extends ExpressionVisitorAdapter {

    private List<ParenthesedSelect> subQueries = new ArrayList<>();

    @Override
    public Object visit(ParenthesedSelect parenthesedSelect, Object context) {
        subQueries.add(parenthesedSelect);
        return null;
    }
    
    @Override
    public Object visit(AndExpression andExpression, Object context) {
        andExpression.getLeftExpression().accept(this, context);
        andExpression.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(OrExpression orExpression, Object context) {
        orExpression.getLeftExpression().accept(this, context);
        orExpression.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(EqualsTo equalsTo, Object context) {
        equalsTo.getLeftExpression().accept(this, context);
        equalsTo.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(NotEqualsTo notEqualsTo, Object context) {
        notEqualsTo.getLeftExpression().accept(this, context);
        notEqualsTo.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(GreaterThan greaterThan, Object context) {
        greaterThan.getLeftExpression().accept(this, context);
        greaterThan.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(GreaterThanEquals greaterThanEquals, Object context) {
        greaterThanEquals.getLeftExpression().accept(this, context);
        greaterThanEquals.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(MinorThan minorThan, Object context) {
        minorThan.getLeftExpression().accept(this, context);
        minorThan.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(MinorThanEquals minorThanEquals, Object context) {
        minorThanEquals.getLeftExpression().accept(this, context);
        minorThanEquals.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(InExpression inExpression, Object context) {
        inExpression.getLeftExpression().accept(this, context);
        if (inExpression.getRightExpression() != null) {
            inExpression.getRightExpression().accept(this, context);
        }
        return null;
    }
    
    @Override
    public Object visit(ExistsExpression existsExpression, Object context) {
        if (existsExpression.getRightExpression() != null) {
            existsExpression.getRightExpression().accept(this, context);
        }
        return null;
    }
    
    @Override
    public Object visit(LikeExpression likeExpression, Object context) {
        likeExpression.getLeftExpression().accept(this, context);
        likeExpression.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(Between between, Object context) {
        between.getLeftExpression().accept(this, context);
        between.getBetweenExpressionStart().accept(this, context);
        between.getBetweenExpressionEnd().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(IsNullExpression isNullExpression, Object context) {
        isNullExpression.getLeftExpression().accept(this, context);
        return null;
    }
    
    // 注意：在JSqlParser 5.3中，Parenthesis的visit方法签名可能发生了变化
    // 如果编译错误，可能需要移除@Override注解或调整方法签名
    public Object visit(Parenthesis parenthesis, Object context) {
        parenthesis.getExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(Function function, Object context) {
        if (function.getParameters() != null && function.getParameters().getExpressions() != null) {
            for (Expression expr : function.getParameters().getExpressions()) {
                expr.accept(this, context);
            }
        }
        return null;
    }
    
    @Override
    public Object visit(CaseExpression caseExpression, Object context) {
        if (caseExpression.getSwitchExpression() != null) {
            caseExpression.getSwitchExpression().accept(this, context);
        }
        if (caseExpression.getWhenClauses() != null) {
            for (WhenClause whenClause : caseExpression.getWhenClauses()) {
                whenClause.getWhenExpression().accept(this, context);
                whenClause.getThenExpression().accept(this, context);
            }
        }
        if (caseExpression.getElseExpression() != null) {
            caseExpression.getElseExpression().accept(this, context);
        }
        return null;
    }
    
    public List<ParenthesedSelect> getSubQueries() {
        return subQueries;
    }
    
    public void clear() {
        subQueries.clear();
    }
}
