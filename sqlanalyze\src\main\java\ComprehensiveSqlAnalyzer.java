import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.util.TablesNamesFinder;

import java.util.*;

/**
 * 综合SQL分析器
 * 遍历所有内层SQL，提取表、过滤条件和关联字段
 */
public class ComprehensiveSqlAnalyzer extends SelectVisitorAdapter<Object> {
    
    private SqlAnalysisResult analysisResult = new SqlAnalysisResult();
    private TablesNamesFinder tablesNamesFinder = new TablesNamesFinder();
    
    @Override
    public Object visit(PlainSelect plainSelect, Object context) {
        try {
            // 获取当前查询涉及的所有表和别名映射
            Map<String, String> tableAliasMap = extractTablesWithAliases(plainSelect);
            Set<String> tables = new HashSet<>(tableAliasMap.values());

            // 创建SQL片段
            String sqlFragment = plainSelect.toString();

            // 分析WHERE条件，获取所有字段
            Set<String> allFilterColumns = new HashSet<>();
            Set<String> allJoinColumns = new HashSet<>();

            if (plainSelect.getWhere() != null) {
                EnhancedWhereAnalyzer whereAnalyzer = new EnhancedWhereAnalyzer(tables, tableAliasMap);
                plainSelect.getWhere().accept(whereAnalyzer);

                // 收集过滤条件和关联字段
                allFilterColumns.addAll(whereAnalyzer.getFilterColumns());
                allJoinColumns.addAll(whereAnalyzer.getJoinColumns());
            }

            // 分析JOIN条件（如果有显式JOIN）
            if (plainSelect.getJoins() != null) {
                for (Join join : plainSelect.getJoins()) {
                    if (join.getOnExpressions() != null) {
                        for (Expression onExpression : join.getOnExpressions()) {
                            EnhancedWhereAnalyzer joinAnalyzer = new EnhancedWhereAnalyzer(tables, tableAliasMap);
                            onExpression.accept(joinAnalyzer);

                            // JOIN条件中的字段都是关联字段
                            allJoinColumns.addAll(joinAnalyzer.getFilterColumns());
                            allJoinColumns.addAll(joinAnalyzer.getJoinColumns());
                        }
                    }
                }
            }

            // 为每个表分别添加结果，使用别名映射确保字段与表的正确对应
            for (Map.Entry<String, String> entry : tableAliasMap.entrySet()) {
                String alias = entry.getKey();
                String tableName = entry.getValue();

                // 筛选属于当前表（通过别名）的过滤字段
                Set<String> tableFilterColumns = new HashSet<>();
                for (String field : allFilterColumns) {
                    if (belongsToTableByAlias(field, alias)) {
                        tableFilterColumns.add(field);
                    }
                }

                // 筛选属于当前表（通过别名）的关联字段
                Set<String> tableJoinColumns = new HashSet<>();
                for (String field : allJoinColumns) {
                    if (belongsToTableByAlias(field, alias)) {
                        tableJoinColumns.add(field);
                    }
                }

                // 为每个表单独添加结果
                analysisResult.addTableResult(tableName, tableFilterColumns, tableJoinColumns, sqlFragment);
            }

            // 递归处理子查询
            processSubQueries(plainSelect);
            
        } catch (Exception e) {
            System.err.println("处理PlainSelect时出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }

    /**
     * 提取表名和别名的映射关系
     * @param plainSelect SQL查询对象
     * @return Map<别名, 表名>
     */
    private Map<String, String> extractTablesWithAliases(PlainSelect plainSelect) {
        Map<String, String> aliasToTableMap = new HashMap<>();

        // 处理FROM子句中的表
        if (plainSelect.getFromItem() instanceof Table) {
            Table table = (Table) plainSelect.getFromItem();
            String tableName = table.getName();
            String alias = table.getAlias() != null ? table.getAlias().getName() : tableName;
            aliasToTableMap.put(alias, tableName);
        }

        // 处理JOIN子句中的表
        if (plainSelect.getJoins() != null) {
            for (Join join : plainSelect.getJoins()) {
                if (join.getRightItem() instanceof Table) {
                    Table table = (Table) join.getRightItem();
                    String tableName = table.getName();
                    String alias = table.getAlias() != null ? table.getAlias().getName() : tableName;
                    aliasToTableMap.put(alias, tableName);
                }
            }
        }

        return aliasToTableMap;
    }

    /**
     * 判断字段是否属于指定的别名
     */
    private boolean belongsToTableByAlias(String field, String alias) {
        if (field.contains(".")) {
            String fieldPrefix = field.substring(0, field.indexOf("."));
            return fieldPrefix.equalsIgnoreCase(alias);
        }
        // 如果没有前缀，可能属于任何表，这种情况需要特殊处理
        return false;
    }

    /**
     * 判断字段是否属于指定的表（保留用于向后兼容）
     */
    private boolean belongsToTable(String field, String table) {
        // 如果字段包含表前缀，直接匹配
        if (field.contains(".")) {
            String prefix = field.substring(0, field.indexOf("."));
            // 检查前缀是否匹配表名或表别名
            return prefix.equalsIgnoreCase(table) ||
                   isTableAlias(prefix, table);
        }
        // 如果没有前缀，假设属于所有表（这种情况下可能需要更复杂的逻辑）
        return true;
    }

    /**
     * 检查是否为表别名（简单实现，可根据需要扩展）
     */
    private boolean isTableAlias(String alias, String tableName) {
        // 简单的别名匹配逻辑：检查别名是否为表名的首字母或简化形式
        if (alias.length() == 1) {
            return tableName.toLowerCase().startsWith(alias.toLowerCase());
        }
        return false;
    }

    @Override
    public Object visit(SetOperationList setOperationList, Object context) {
        // 处理UNION、INTERSECT、EXCEPT等集合操作
        for (Select select : setOperationList.getSelects()) {
            select.accept(this, context);
        }
        return null;
    }
    
    @Override
    public Object visit(WithItem withItem, Object context) {
        // 处理WITH子句（CTE - Common Table Expression）
        if (withItem.getSelect() != null) {
            withItem.getSelect().accept(this, context);
        }
        return null;
    }
    
    @Override
    public Object visit(Values values, Object context) {
        // VALUES子句通常不需要特殊处理
        return null;
    }
    
    /**
     * 从PlainSelect中提取所有表名
     */
    private Set<String> extractTablesFromPlainSelect(PlainSelect plainSelect) {
        Set<String> tables = new HashSet<>();
        
        try {
            // 使用TablesNamesFinder提取表名
            String sql = plainSelect.toString();
            Set<String> foundTables = tablesNamesFinder.findTables(sql);
            tables.addAll(foundTables);
            
        } catch (Exception e) {
            // 如果TablesNamesFinder失败，手动提取
            System.err.println("TablesNamesFinder失败，尝试手动提取: " + e.getMessage());
            
            // 从FROM子句提取
            if (plainSelect.getFromItem() != null) {
                extractTableFromFromItem(plainSelect.getFromItem(), tables);
            }
            
            // 从JOIN子句提取
            if (plainSelect.getJoins() != null) {
                for (Join join : plainSelect.getJoins()) {
                    if (join.getRightItem() != null) {
                        extractTableFromFromItem(join.getRightItem(), tables);
                    }
                }
            }
        }
        
        return tables;
    }
    
    /**
     * 从FromItem中提取表名
     */
    private void extractTableFromFromItem(FromItem fromItem, Set<String> tables) {
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            tables.add(table.getName());
        } else if (fromItem instanceof ParenthesedSelect) {
            // 子查询会在递归中处理
        }
        // 其他类型的FromItem可以根据需要添加处理
    }
    
    /**
     * 处理子查询
     */
    private void processSubQueries(PlainSelect plainSelect) {
        // 处理FROM子句中的子查询
        if (plainSelect.getFromItem() instanceof ParenthesedSelect) {
            ParenthesedSelect parenthesedSelect = (ParenthesedSelect) plainSelect.getFromItem();
            parenthesedSelect.getSelect().accept(this, null);
        }

        // 处理JOIN中的子查询
        if (plainSelect.getJoins() != null) {
            for (Join join : plainSelect.getJoins()) {
                if (join.getRightItem() instanceof ParenthesedSelect) {
                    ParenthesedSelect parenthesedSelect = (ParenthesedSelect) join.getRightItem();
                    parenthesedSelect.getSelect().accept(this, null);
                }
            }
        }
        
        // 处理WHERE条件中的子查询
        if (plainSelect.getWhere() != null) {
            SubQueryFinder subQueryFinder = new SubQueryFinder();
            plainSelect.getWhere().accept(subQueryFinder);
            for (ParenthesedSelect parenthesedSelect : subQueryFinder.getSubQueries()) {
                parenthesedSelect.getSelect().accept(this, null);
            }
        }

        // 处理SELECT列表中的子查询
        // 注意：在JSqlParser 5.3中，SelectExpressionItem已被移除
        // 这里我们简化处理，主要关注FROM和WHERE子句中的子查询
        // 如果需要处理SELECT列表中的子查询，需要根据具体的SelectItem实现来处理
        if (plainSelect.getSelectItems() != null) {
            // 暂时跳过SELECT列表中的子查询处理
            // 因为API变化，需要更详细的文档来正确实现
        }
    }
    
    /**
     * 获取分析结果
     */
    public SqlAnalysisResult getAnalysisResult() {
        return analysisResult;
    }
}
