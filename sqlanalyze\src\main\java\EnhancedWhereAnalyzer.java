import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.*;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.select.ParenthesedSelect;

import java.util.*;

/**
 * 增强的WHERE条件分析器
 * 能够区分过滤条件和表关联条件
 */
public class EnhancedWhereAnalyzer extends ExpressionVisitorAdapter {
    
    private Set<String> filterColumns = new HashSet<>();    // 过滤条件字段
    private Set<String> joinColumns = new HashSet<>();      // 关联条件字段
    private Set<String> allTables = new HashSet<>();        // 当前查询涉及的所有表
    
    public EnhancedWhereAnalyzer(Set<String> tables) {
        this.allTables = new HashSet<>(tables);
    }
    
    @Override
    public Object visit(AndExpression andExpression, Object context) {
        // 递归处理AND表达式的左右两边
        andExpression.getLeftExpression().accept(this, context);
        andExpression.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(OrExpression orExpression, Object context) {
        // 递归处理OR表达式的左右两边
        orExpression.getLeftExpression().accept(this, context);
        orExpression.getRightExpression().accept(this, context);
        return null;
    }
    
    @Override
    public Object visit(EqualsTo equalsTo, Object context) {
        analyzeComparison(equalsTo.getLeftExpression(), equalsTo.getRightExpression());
        return null;
    }
    
    @Override
    public Object visit(NotEqualsTo notEqualsTo, Object context) {
        analyzeComparison(notEqualsTo.getLeftExpression(), notEqualsTo.getRightExpression());
        return null;
    }
    
    @Override
    public Object visit(GreaterThan greaterThan, Object context) {
        analyzeComparison(greaterThan.getLeftExpression(), greaterThan.getRightExpression());
        return null;
    }
    
    @Override
    public Object visit(GreaterThanEquals greaterThanEquals, Object context) {
        analyzeComparison(greaterThanEquals.getLeftExpression(), greaterThanEquals.getRightExpression());
        return null;
    }
    
    @Override
    public Object visit(MinorThan minorThan, Object context) {
        analyzeComparison(minorThan.getLeftExpression(), minorThan.getRightExpression());
        return null;
    }
    
    @Override
    public Object visit(MinorThanEquals minorThanEquals, Object context) {
        analyzeComparison(minorThanEquals.getLeftExpression(), minorThanEquals.getRightExpression());
        return null;
    }
    
    @Override
    public Object visit(InExpression inExpression, Object context) {
        // 处理IN表达式
        Expression leftExpression = inExpression.getLeftExpression();
        if (leftExpression instanceof Column) {
            Column column = (Column) leftExpression;
            
            // 检查右边是否是子查询
            if (inExpression.getRightExpression() instanceof ParenthesedSelect) {
                // IN子查询通常是过滤条件
                filterColumns.add(column.toString());
            } else {
                // IN列表通常也是过滤条件
                filterColumns.add(column.toString());
            }
        }
        return null;
    }
    
    @Override
    public Object visit(LikeExpression likeExpression, Object context) {
        // LIKE表达式通常是过滤条件
        Expression leftExpression = likeExpression.getLeftExpression();
        if (leftExpression instanceof Column) {
            filterColumns.add(leftExpression.toString());
        }
        return null;
    }
    
    @Override
    public Object visit(IsNullExpression isNullExpression, Object context) {
        // IS NULL表达式通常是过滤条件
        Expression leftExpression = isNullExpression.getLeftExpression();
        if (leftExpression instanceof Column) {
            filterColumns.add(leftExpression.toString());
        }
        return null;
    }
    
    @Override
    public Object visit(Between between, Object context) {
        // BETWEEN表达式通常是过滤条件
        Expression leftExpression = between.getLeftExpression();
        if (leftExpression instanceof Column) {
            filterColumns.add(leftExpression.toString());
        }
        return null;
    }
    
    /**
     * 分析比较表达式，判断是关联条件还是过滤条件
     */
    private void analyzeComparison(Expression left, Expression right) {
        boolean leftIsColumn = left instanceof Column;
        boolean rightIsColumn = right instanceof Column;
        
        if (leftIsColumn && rightIsColumn) {
            // 两边都是字段，很可能是表关联条件
            Column leftCol = (Column) left;
            Column rightCol = (Column) right;
            
            String leftTable = getTableFromColumn(leftCol);
            String rightTable = getTableFromColumn(rightCol);
            
            // 如果来自不同的表，则认为是关联条件
            if (leftTable != null && rightTable != null && !leftTable.equals(rightTable)) {
                joinColumns.add(leftCol.toString());
                joinColumns.add(rightCol.toString());
            } else {
                // 同一个表的字段比较，可能是过滤条件
                filterColumns.add(leftCol.toString());
                if (!leftCol.toString().equals(rightCol.toString())) {
                    filterColumns.add(rightCol.toString());
                }
            }
        } else if (leftIsColumn) {
            // 左边是字段，右边是值/参数/函数，通常是过滤条件
            filterColumns.add(left.toString());
        } else if (rightIsColumn) {
            // 右边是字段，左边是值/参数/函数，通常是过滤条件
            filterColumns.add(right.toString());
        }
    }
    
    /**
     * 从字段中提取表名
     */
    private String getTableFromColumn(Column column) {
        if (column.getTable() != null) {
            return column.getTable().getName();
        }
        
        // 如果没有明确的表名，尝试从字段名中推断
        String columnStr = column.toString();
        if (columnStr.contains(".")) {
            String prefix = columnStr.substring(0, columnStr.indexOf("."));
            // 检查这个前缀是否是已知的表名或别名
            for (String table : allTables) {
                if (table.toLowerCase().contains(prefix.toLowerCase()) || 
                    prefix.toLowerCase().equals(table.toLowerCase())) {
                    return prefix;
                }
            }
        }
        
        return null;
    }
    
    public Set<String> getFilterColumns() {
        return filterColumns;
    }
    
    public Set<String> getJoinColumns() {
        return joinColumns;
    }
    
    public void printResults() {
        System.out.println("过滤条件字段: " + filterColumns);
        System.out.println("关联条件字段: " + joinColumns);
    }
}
