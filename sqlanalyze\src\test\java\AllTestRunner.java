/**
 * 所有测试的运行器
 * 按照一个Java类对应一个测试类的原则整理
 */
public class AllTestRunner {
    
    public static void main(String[] args) {
        System.out.println("=== SQL分析器测试套件 ===");
        System.out.println("按照一个Java类对应一个测试类的原则整理");
        System.out.println();
        
        try {
            // 1. SqlFileBatchAnalyzer 测试
            System.out.println("🚀 运行 SqlFileBatchAnalyzer 测试...");
            SqlFileBatchAnalyzerTest.main(new String[]{});
            
            // 2. AggregatedTableFieldResult 测试
            System.out.println("🚀 运行 AggregationTest 测试...");
            AggregationTest.main(new String[]{});
            
            // 3. TableFieldAnalysisResult 测试
            System.out.println("🚀 运行 TableFieldAnalysisTest 测试...");
            TableFieldAnalysisTest.main(new String[]{});
            
            // 4. CSV输出测试
            System.out.println("🚀 运行 CsvOutputTest 测试...");
            CsvOutputTest.main(new String[]{});
            
            // 5. 编译测试
            System.out.println("🚀 运行 CompilationTest 测试...");
            CompilationTest.main(new String[]{});
            
            // 6. API兼容性测试
            System.out.println("🚀 运行 ApiCompatibilityTest 测试...");
            ApiCompatibilityTest.main(new String[]{});

            // 7. 字段名清理功能测试
            System.out.println("🚀 运行 FieldNameCleaningTest 测试...");
            FieldNameCleaningTest.main(new String[]{});

            // 8. GroupKey聚合功能测试
            System.out.println("🚀 运行 GroupKeyAggregationTest 测试...");
            GroupKeyAggregationTest.main(new String[]{});

            // 9. Java 8兼容性测试
            System.out.println("🚀 运行 Java8CompatibilityTest 测试...");
            Java8CompatibilityTest.main(new String[]{});

            System.out.println("\n🎉 所有测试完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试运行过程中出现错误:");
            e.printStackTrace();
        }
    }
}
