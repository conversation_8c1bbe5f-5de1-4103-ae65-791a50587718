import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.ExpressionVisitorAdapter;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.schema.Column;
import java.util.HashSet;
import java.util.Set;

public class WhereColumnFinder extends ExpressionVisitorAdapter {
    private final Set<String> columnNames = new HashSet<>();

    @Override
    public Object visit(Column column,Object obj) {
        columnNames.add(column.toString());
        return null;
    }

     @Override
    public Object visit(AndExpression arg0, Object arg1) {
        arg0.getLeftExpression().accept(this, arg1);
        arg0.getRightExpression().accept(this, arg1);
        return null;
    }

    public void printColumnNames() {
        System.out.println("WHERE clause fields:");
        for (String columnName : columnNames) {
            System.out.println(columnName);
        }
    }
}