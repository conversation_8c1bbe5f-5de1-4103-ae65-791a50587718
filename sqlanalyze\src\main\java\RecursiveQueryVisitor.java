import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import net.sf.jsqlparser.statement.piped.FromQuery;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.Statement;
import java.util.Set;

public class RecursiveQueryVisitor extends SelectVisitorAdapter<Object> {
    private final Set<String> allColumns;
    private final WhereColumnFinder whereColumnFinder;

    public RecursiveQueryVisitor(Set<String> allColumns) {
        this.allColumns = allColumns;
        this.whereColumnFinder = new WhereColumnFinder();
    }


    @Override
    public Object visit(PlainSelect arg0, Object arg1) {
        if (arg0.getWhere() != null) {
            arg0.getWhere().accept(whereColumnFinder);
        }

        // Recursively visit the nested SQL statements
        if (arg0.getFromItem() instanceof Select) {
            Select subSelect = (Select) arg0.getFromItem();
            subSelect.accept(this, arg1);
        }
        return null;
}

    @Override
    public Object visit(FromQuery arg0, Object arg1) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object visit(SetOperationList arg0, Object arg1) {
        for (Select select : arg0.getSelects()) {
            select.accept(this, arg1);
        }
        return null;
}

    @Override
    public Object visit(WithItem arg0, Object arg1) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object visit(Values arg0, Object arg1) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object visit(LateralSubSelect arg0, Object arg1) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object visit(TableStatement arg0, Object arg1) {
        // TODO Auto-generated method stub
        return null;
    }

    public void finishVisit() {
        whereColumnFinder.printColumnNames();
    }

}