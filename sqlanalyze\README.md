# My Java Project

This is a simple Java application project structured to demonstrate the use of Gradle for building and testing.

## Project Structure

```
my-java-project
├── src
│   ├── main
│   │   ├── java
│   │   │   └── App.java
│   │   └── resources
│   └── test
│       ├── java
│       │   └── AppTest.java
│       └── resources
├── .gitignore
├── build.gradle
└── README.md
```

## Getting Started

### Prerequisites

- Java Development Kit (JDK) 8 or higher
- Gradle 6.0 or higher

### Building the Project

To build the project, navigate to the project directory and run:

```
gradle build
```

### Running the Application

To run the application, use the following command:

```
gradle run
```

### Running Tests

To execute the tests, use:

```
gradle test
```

## Contributing

Feel free to submit issues or pull requests for improvements or bug fixes.