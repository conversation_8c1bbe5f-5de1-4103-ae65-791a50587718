import java.util.HashSet;
import java.util.Set;

/**
 * 测试新的表+过滤字段分组分析结果
 */
public class TableFieldAnalysisTest {
    public static void main(String[] args) {
        System.out.println("=== 测试表+过滤字段分组分析 ===");

        // 创建分析结果
        SqlAnalysisResult sqlResult = new SqlAnalysisResult();

        // 模拟复杂SQL的分析结果
        createTestData(sqlResult);

        // 打印结果
        sqlResult.printResults();

        System.out.println("\n=== 预期输出格式示例 ===");
        System.out.println("T_D_AI_STOCK：关联字段[C_DAI_CODE],过滤字段[C_MKT_CODE, C_PORT_CODE, D_STOCK],所有字段[C_DAI_CODE, C_MKT_CODE, C_PORT_CODE, D_STOCK],出现次数2");
        System.out.println("T_S_DAI_ITEM：关联字段[C_DAI_CODE],过滤字段[],所有字段[C_DAI_CODE],出现次数1");

        System.out.println("\n=== 测试完成 ===");
    }
    
    private static void createTestData(SqlAnalysisResult sqlResult) {
        // 第一个查询：T_D_AI_STOCK A, T_S_DAI_ITEM B
        Set<String> tables1 = new HashSet<>();
        tables1.add("T_D_AI_STOCK");
        tables1.add("T_S_DAI_ITEM");

        Set<String> filterColumns1 = new HashSet<>();
        filterColumns1.add("A.C_PORT_CODE");
        filterColumns1.add("A.D_STOCK");
        filterColumns1.add("A.C_MKT_CODE");

        Set<String> joinColumns1 = new HashSet<>();
        joinColumns1.add("A.C_DAI_CODE");
        joinColumns1.add("B.C_DAI_CODE");

        String sql1 = "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM T_D_AI_STOCK A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3";
        sqlResult.addPlainSelectResult(tables1, filterColumns1, joinColumns1, sql1);

        // 第二个查询：相同的表组合，相同的过滤字段（测试累计功能）
        Set<String> tables2 = new HashSet<>();
        tables2.add("T_D_AI_STOCK");
        tables2.add("T_S_DAI_ITEM");

        Set<String> filterColumns2 = new HashSet<>();
        filterColumns2.add("A.C_PORT_CODE");
        filterColumns2.add("A.D_STOCK");
        filterColumns2.add("A.C_MKT_CODE");

        Set<String> joinColumns2 = new HashSet<>();
        joinColumns2.add("A.C_DAI_CODE");
        joinColumns2.add("B.C_DAI_CODE");
        joinColumns2.add("A.C_EXTRA_FIELD"); // 额外的关联字段，测试合并功能

        String sql2 = "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM T_D_AI_STOCK A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3 AND A.C_MKT_CODE = $4";
        sqlResult.addPlainSelectResult(tables2, filterColumns2, joinColumns2, sql2);

        // 第三个查询：不同的表组合
        Set<String> tables3 = new HashSet<>();
        tables3.add("T_D_AI_ACT_VAL");
        tables3.add("T_S_DAI_ITEM");

        Set<String> filterColumns3 = new HashSet<>();
        filterColumns3.add("A.C_PORT_CODE");
        filterColumns3.add("A.D_CHK_ACC");

        Set<String> joinColumns3 = new HashSet<>();
        joinColumns3.add("A.C_DAI_CODE");
        joinColumns3.add("B.C_DAI_CODE");

        String sql3 = "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM T_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $5";
        sqlResult.addPlainSelectResult(tables3, filterColumns3, joinColumns3, sql3);

        // 第四个查询：只有关联字段，没有过滤字段
        Set<String> tables4 = new HashSet<>();
        tables4.add("R_D_AI_ACT_VAL");
        tables4.add("T_S_DAI_ITEM");

        Set<String> filterColumns4 = new HashSet<>(); // 空的过滤字段

        Set<String> joinColumns4 = new HashSet<>();
        joinColumns4.add("A.C_DAI_CODE");
        joinColumns4.add("B.C_DAI_CODE");

        String sql4 = "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM R_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE";
        sqlResult.addPlainSelectResult(tables4, filterColumns4, joinColumns4, sql4);

        System.out.println("已创建 4 个测试查询");
    }
}
