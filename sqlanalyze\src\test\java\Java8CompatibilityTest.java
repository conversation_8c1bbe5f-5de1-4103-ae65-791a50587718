/**
 * Java 8兼容性测试
 * 验证代码是否使用了Java 8兼容的语法
 */
public class Java8CompatibilityTest {
    
    public static void main(String[] args) {
        System.out.println("=== Java 8兼容性测试 ===");
        System.out.println("验证代码是否使用了Java 8兼容的语法");
        System.out.println();
        
        try {
            // 测试GroupKeyAggregationTest是否能正常编译和运行
            System.out.println("🔍 测试GroupKeyAggregationTest...");
            GroupKeyAggregationTest.main(new String[]{});
            System.out.println("✅ GroupKeyAggregationTest Java 8兼容性测试通过");
            
            // 测试其他可能使用高版本语法的类
            System.out.println("\n🔍 测试FieldNameCleaningTest...");
            FieldNameCleaningTest.main(new String[]{});
            System.out.println("✅ FieldNameCleaningTest Java 8兼容性测试通过");
            
            System.out.println("\n🎉 所有Java 8兼容性测试通过！");
            System.out.println("项目代码符合Java 8语法要求");
            
        } catch (Exception e) {
            System.err.println("❌ Java 8兼容性测试失败:");
            e.printStackTrace();
            
            System.out.println("\n🔧 可能的问题:");
            System.out.println("1. 使用了Java 9+的语法特性（如var关键字）");
            System.out.println("2. 使用了Java 8不支持的API");
            System.out.println("3. 缺少必要的import语句");
        }
    }
}
