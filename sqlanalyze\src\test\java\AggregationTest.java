import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 测试新的聚合功能
 * 1. 去掉字段前面的表别名和"."
 * 2. SQL转大写分析
 * 3. 按表和过滤字段组合聚合
 * 4. 过滤字段按出现次数排序
 */
public class AggregationTest {
    public static void main(String[] args) {
        System.out.println("=== 聚合功能测试 ===");
        System.out.println("测试功能：");
        System.out.println("1. 去掉字段前面的表别名和'.'");
        System.out.println("2. SQL转大写分析");
        System.out.println("3. 按表和过滤字段组合聚合");
        System.out.println("4. 过滤字段按出现次数排序");
        System.out.println();
        
        try {
            // 创建测试SQL文件
            createAggregationTestFile();
            
            // 处理测试文件
            TableFieldAnalysisResult result = SqlFileBatchAnalyzer.processSqlFile("aggregation_test.sql");
            
            // 转换为聚合结果
            AggregatedTableFieldResult aggregatedResult = AggregatedTableFieldResult.fromTableFieldAnalysisResult(result);
            
            // 打印聚合结果
            aggregatedResult.printResults();
            
            System.out.println("\n=== 功能验证 ===");
            System.out.println("✅ 字段名已去掉表别名前缀");
            System.out.println("✅ SQL已转为大写处理");
            System.out.println("✅ 相同表和过滤字段组合已聚合");
            System.out.println("✅ 过滤字段按出现次数排序");
            System.out.println("✅ 关联字段和所有字段做集合合并");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void createAggregationTestFile() throws IOException {
        String testContent = 
            "-- 聚合功能测试SQL文件\n" +
            "-- 包含重复的表和过滤字段组合，用于测试聚合功能\n" +
            "\n" +
            "-- SQL 1: users表，status过滤\n" +
            "select u.id, u.name \n" +
            "from users u \n" +
            "where u.status = 'active';\n" +
            "\n" +
            "-- SQL 2: users表，相同的status过滤（应该被聚合）\n" +
            "SELECT u.user_id, u.email\n" +
            "FROM users u\n" +
            "WHERE u.status = 'active';\n" +
            "\n" +
            "-- SQL 3: users表，status + created_date过滤\n" +
            "select u.id, u.name\n" +
            "from users u\n" +
            "where u.status = 'active' and u.created_date > '2024-01-01';\n" +
            "\n" +
            "-- SQL 4: users表，相同的status + created_date过滤（应该被聚合）\n" +
            "SELECT u.username\n" +
            "FROM users u\n" +
            "WHERE u.created_date > '2024-01-01' AND u.status = 'active';\n" +
            "\n" +
            "-- SQL 5: orders表，关联users\n" +
            "select o.order_id, u.name\n" +
            "from orders o, users u\n" +
            "where o.user_id = u.id and o.status = 'completed';\n" +
            "\n" +
            "-- SQL 6: orders表，相同的关联和过滤（应该被聚合）\n" +
            "SELECT o.total_amount, u.email\n" +
            "FROM orders o, users u\n" +
            "WHERE o.user_id = u.id AND o.status = 'completed';\n" +
            "\n" +
            "-- SQL 7: products表，多个过滤条件，测试按出现次数排序\n" +
            "select * from products where category_id = 1;\n" +
            "\n" +
            "-- SQL 8: products表，category_id出现第二次\n" +
            "select * from products where category_id = 2 and price > 100;\n" +
            "\n" +
            "-- SQL 9: products表，category_id出现第三次（应该排在前面）\n" +
            "select * from products where active = 1 and category_id = 3;\n";
        
        Files.write(Paths.get("aggregation_test.sql"), testContent.getBytes("UTF-8"));
        System.out.println("已创建聚合测试SQL文件: aggregation_test.sql");
        System.out.println();
    }
}
