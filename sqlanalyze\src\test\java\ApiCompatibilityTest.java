import java.util.HashSet;
import java.util.Set;

/**
 * API兼容性测试 - 验证新的分组统计功能
 */
public class ApiCompatibilityTest {
    public static void main(String[] args) {
        System.out.println("=== 新的表+过滤字段分组统计测试 ===");

        // 创建分析结果对象
        SqlAnalysisResult result = new SqlAnalysisResult();

        System.out.println("测试新的分组统计功能...");

        // 模拟一个复杂的SQL分析结果，展示我们的分析器能力
        demonstrateApiCompatibility(result);

        // 打印分析结果
        result.printResults();

        System.out.println("=== 新功能说明 ===");
        System.out.println("1. 按表+过滤字段组合进行分组统计");
        System.out.println("2. 相同组合的查询会累计次数");
        System.out.println("3. 关联字段会自动合并");
        System.out.println("4. 字段名自动清理（去掉表别名前缀）");

        System.out.println("\n=== 测试完成 ===");
    }
    
    private static void demonstrateApiCompatibility(SqlAnalysisResult result) {
        // 测试案例1：简单的两表关联查询
        Set<String> tables1 = new HashSet<>();
        tables1.add("users");
        tables1.add("orders");

        Set<String> filterColumns1 = new HashSet<>();
        filterColumns1.add("a.status");

        Set<String> joinColumns1 = new HashSet<>();
        joinColumns1.add("a.id");
        joinColumns1.add("b.user_id");

        String sql1 = "SELECT a.id, b.name FROM users a, orders b WHERE a.id = b.user_id AND a.status = 'active'";
        result.addPlainSelectResult(tables1, filterColumns1, joinColumns1, sql1);

        // 测试案例2：包含子查询的复杂查询
        Set<String> tables2 = new HashSet<>();
        tables2.add("products");

        Set<String> filterColumns2 = new HashSet<>();
        filterColumns2.add("category_id");

        Set<String> joinColumns2 = new HashSet<>();

        String sql2 = "SELECT * FROM products WHERE category_id IN (SELECT id FROM categories WHERE active = 1)";
        result.addPlainSelectResult(tables2, filterColumns2, joinColumns2, sql2);

        // 子查询
        Set<String> tables3 = new HashSet<>();
        tables3.add("categories");

        Set<String> filterColumns3 = new HashSet<>();
        filterColumns3.add("active");

        Set<String> joinColumns3 = new HashSet<>();

        String sql3 = "SELECT id FROM categories WHERE active = 1";
        result.addPlainSelectResult(tables3, filterColumns3, joinColumns3, sql3);

        // 测试案例3：UNION查询
        Set<String> tables4 = new HashSet<>();
        tables4.add("sales");

        Set<String> filterColumns4 = new HashSet<>();
        filterColumns4.add("date");

        Set<String> joinColumns4 = new HashSet<>();

        String sql4 = "SELECT customer_id, amount FROM sales WHERE date >= '2024-01-01'";
        result.addPlainSelectResult(tables4, filterColumns4, joinColumns4, sql4);

        Set<String> tables5 = new HashSet<>();
        tables5.add("refunds");

        Set<String> filterColumns5 = new HashSet<>();
        filterColumns5.add("date");

        Set<String> joinColumns5 = new HashSet<>();

        String sql5 = "SELECT customer_id, amount FROM refunds WHERE date >= '2024-01-01'";
        result.addPlainSelectResult(tables5, filterColumns5, joinColumns5, sql5);

        // 测试案例4：相同模式的查询（会被分组）
        Set<String> tables6 = new HashSet<>();
        tables6.add("sales");

        Set<String> filterColumns6 = new HashSet<>();
        filterColumns6.add("date");

        Set<String> joinColumns6 = new HashSet<>();

        String sql6 = "SELECT customer_id, amount FROM sales WHERE date >= '2024-02-01'";
        result.addPlainSelectResult(tables6, filterColumns6, joinColumns6, sql6);

        System.out.println("已添加 6 个测试查询");
    }
}
