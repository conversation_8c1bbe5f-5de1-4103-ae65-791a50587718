import java.util.HashSet;
import java.util.Set;

/**
 * 测试新的API功能
 */
public class NewApiTest {
    public static void main(String[] args) {
        System.out.println("=== 测试新的表+过滤字段分组功能 ===");
        
        // 创建SqlAnalysisResult实例
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // 测试案例1：相同表+相同过滤字段的查询（应该被分组并累计次数）
        Set<String> tables1 = new HashSet<>();
        tables1.add("USERS");
        
        Set<String> filterColumns1 = new HashSet<>();
        filterColumns1.add("status");
        filterColumns1.add("active");
        
        Set<String> joinColumns1 = new HashSet<>();
        joinColumns1.add("id");
        
        String sql1 = "SELECT * FROM users WHERE status = 'active' AND active = 1";
        result.addPlainSelectResult(tables1, filterColumns1, joinColumns1, sql1);
        
        // 测试案例2：相同表+相同过滤字段的另一个查询（应该累计到案例1）
        Set<String> tables2 = new HashSet<>();
        tables2.add("USERS");
        
        Set<String> filterColumns2 = new HashSet<>();
        filterColumns2.add("status");
        filterColumns2.add("active");
        
        Set<String> joinColumns2 = new HashSet<>();
        joinColumns2.add("user_id");  // 不同的关联字段，应该被合并
        
        String sql2 = "SELECT name FROM users WHERE status = 'pending' AND active = 1";
        result.addPlainSelectResult(tables2, filterColumns2, joinColumns2, sql2);
        
        // 测试案例3：不同的过滤字段组合
        Set<String> tables3 = new HashSet<>();
        tables3.add("USERS");
        
        Set<String> filterColumns3 = new HashSet<>();
        filterColumns3.add("email");
        filterColumns3.add("verified");
        
        Set<String> joinColumns3 = new HashSet<>();
        
        String sql3 = "SELECT * FROM users WHERE email LIKE '%@example.com' AND verified = 1";
        result.addPlainSelectResult(tables3, filterColumns3, joinColumns3, sql3);
        
        // 测试案例4：不同的表
        Set<String> tables4 = new HashSet<>();
        tables4.add("ORDERS");
        
        Set<String> filterColumns4 = new HashSet<>();
        filterColumns4.add("status");
        
        Set<String> joinColumns4 = new HashSet<>();
        joinColumns4.add("user_id");
        joinColumns4.add("product_id");
        
        String sql4 = "SELECT * FROM orders WHERE status = 'completed'";
        result.addPlainSelectResult(tables4, filterColumns4, joinColumns4, sql4);
        
        // 打印结果
        result.printResults();
        
        System.out.println("\n=== 验证预期结果 ===");
        System.out.println("预期应该看到：");
        System.out.println("1. USERS表有2个不同的过滤字段组合");
        System.out.println("2. 第一个组合(active,status)出现2次，关联字段合并了id和user_id");
        System.out.println("3. 第二个组合(email,verified)出现1次");
        System.out.println("4. ORDERS表有1个组合，出现1次");
        
        System.out.println("\n=== 测试完成 ===");
    }
}
