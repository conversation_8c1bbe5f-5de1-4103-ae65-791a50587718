-- 字段名清理功能测试SQL文件
-- 测试各种表别名和字段名的清理

-- 测试1：简单表别名
SELECT u.id, u.name, o.amount 
FROM users u, orders o 
WHERE u.id = o.user_id AND u.status = 'active';

-- 测试2：复杂表别名
SELECT A.C_SEC_CODE, SEC.C_SEC_VAR_CODE, STOCK.C_DV_ACCOUNT_CODE 
FROM T_D_AC_TRADE_IVT A 
LEFT JOIN T_P_SV_SEC_BASE SEC ON A.C_SEC_CODE = SEC.C_SEC_CODE 
LEFT JOIN T_D_MP_PRE_STOCK STOCK ON A.C_SEC_CODE = STOCK.C_SEC_CODE 
WHERE A.C_PORT_CODE = 'PORT001' AND A.C_DT_CODE = 'BUY';

-- 测试3：混合情况（有些字段有别名，有些没有）
SELECT u.name, profiles.title, o.amount 
FROM users u 
JOIN profiles ON u.id = profiles.user_id 
JOIN orders o ON u.id = o.user_id 
WHERE u.created_date > '2024-01-01' AND status = 'completed';

-- 测试4：长表名和字段名
SELECT very_long_table_alias.very_long_field_name, 
       short.id,
       another_table.another_field
FROM very_long_table_name very_long_table_alias,
     short_table short,
     another_table
WHERE very_long_table_alias.id = short.ref_id 
    AND short.status = 'active'
    AND another_table.type = 'test';
