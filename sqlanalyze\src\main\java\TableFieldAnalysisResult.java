import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 按表组织的字段分析结果
 * 输出格式：table1：关联字段{a,b},过滤字段{c,d},所有字段{a,b,c,d}
 */
public class TableFieldAnalysisResult {
    
    /**
     * 单个表的字段信息
     */
    public static class TableFieldInfo {
        private String tableName;
        private Set<String> joinFields = new HashSet<>();     // 关联字段
        private Map<String, Integer> filterFieldCounts = new HashMap<>(); // 过滤字段及其出现次数
        private Set<String> allFields = new HashSet<>();      // 所有字段

        public TableFieldInfo(String tableName) {
            this.tableName = tableName;
        }

        public void addJoinField(String field) {
            // 字段名已在QueryInfo中清理，直接使用
            joinFields.add(field);
            allFields.add(field);
        }

        public void addFilterField(String field) {
            // 字段名已在QueryInfo中清理，直接使用
            filterFieldCounts.put(field, filterFieldCounts.getOrDefault(field, 0) + 1);
            allFields.add(field);
        }

        public void addField(String field) {
            // 字段名已在QueryInfo中清理，直接使用
            allFields.add(field);
        }

        /**
         * 获取按出现次数排序的过滤字段列表
         */
        public List<String> getSortedFilterFields() {
            List<Map.Entry<String, Integer>> entries = new ArrayList<>(filterFieldCounts.entrySet());
            entries.sort((e1, e2) -> e2.getValue().compareTo(e1.getValue())); // 按次数降序

            List<String> result = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : entries) {
                result.add(entry.getKey());
            }
            return result;
        }

        /**
         * 合并另一个TableFieldInfo到当前对象
         */
        public void merge(TableFieldInfo other) {
            // 合并关联字段
            this.joinFields.addAll(other.joinFields);

            // 合并过滤字段计数
            for (Map.Entry<String, Integer> entry : other.filterFieldCounts.entrySet()) {
                this.filterFieldCounts.put(entry.getKey(),
                    this.filterFieldCounts.getOrDefault(entry.getKey(), 0) + entry.getValue());
            }

            // 合并所有字段
            this.allFields.addAll(other.allFields);
        }
        
        // Getters
        public String getTableName() { return tableName; }
        public Set<String> getJoinFields() { return joinFields; }
        public Set<String> getFilterFields() {
            return new HashSet<>(filterFieldCounts.keySet());
        }
        public Set<String> getAllFields() { return allFields; }
        public Map<String, Integer> getFilterFieldCounts() { return filterFieldCounts; }

        @Override
        public String toString() {
            List<String> sortedFilterFields = getSortedFilterFields();
            String filterFieldsStr = String.join(",", sortedFilterFields);
            return String.format("%s：关联字段%s,过滤字段[%s],所有字段%s",
                               tableName, joinFields, filterFieldsStr, allFields);
        }
    }
    
    // 按表名存储字段信息
    private Map<String, TableFieldInfo> tableFieldMap = new HashMap<>();
    
    /**
     * 添加查询信息到结果中
     */
    public void addQueryInfo(SqlAnalysisResult.QueryInfo queryInfo) {
        // 处理每个表
        for (String table : queryInfo.getTables()) {
            TableFieldInfo tableInfo = tableFieldMap.computeIfAbsent(table, TableFieldInfo::new);
            
            // 添加关联字段（只添加属于当前表的字段）
            for (String joinField : queryInfo.getJoinColumns()) {
                if (belongsToTable(joinField, table)) {
                    tableInfo.addJoinField(joinField);
                }
            }
            
            // 添加过滤字段（只添加属于当前表的字段）
            for (String filterField : queryInfo.getFilterColumns()) {
                if (belongsToTable(filterField, table)) {
                    tableInfo.addFilterField(filterField);
                }
            }
        }
    }
    
    /**
     * 判断字段是否属于指定的表
     */
    private boolean belongsToTable(String field, String table) {
        // 如果字段包含表前缀，直接匹配
        if (field.contains(".")) {
            String prefix = field.substring(0, field.indexOf("."));
            // 检查前缀是否匹配表名或表别名
            return prefix.equalsIgnoreCase(table) ||
                   isTableAlias(prefix, table);
        }

        // 如果没有前缀，假设属于所有表（这种情况下可能需要更复杂的逻辑）
        return true;
    }
    
    /**
     * 检查是否是表别名
     */
    private boolean isTableAlias(String alias, String tableName) {
        // 改进的别名匹配逻辑
        // 例如：A 可能是 T_D_AI_STOCK 的别名，B 可能是 T_S_DAI_ITEM 的别名
        if (alias.length() == 1) {
            // 根据常见的SQL别名模式进行匹配
            String upperAlias = alias.toUpperCase();
            String upperTable = tableName.toUpperCase();

            // 检查是否是表名的首字母
            if (upperTable.startsWith(upperAlias)) {
                return true;
            }

            // 检查特定的别名模式
            // A -> T_D_AI_STOCK, T_D_AI_ACT_VAL, R_D_AI_ACT_VAL (所有以A开头的表用A别名)
            // B -> T_S_DAI_ITEM (B别名)
            if ("A".equals(upperAlias)) {
                return upperTable.contains("AI_STOCK") || upperTable.contains("AI_ACT_VAL");
            } else if ("B".equals(upperAlias)) {
                return upperTable.contains("DAI_ITEM");
            }
        }
        return false;
    }
    
    /**
     * 从SqlAnalysisResult构建TableFieldAnalysisResult
     */
    public static TableFieldAnalysisResult fromSqlAnalysisResult(SqlAnalysisResult sqlResult) {
        TableFieldAnalysisResult result = new TableFieldAnalysisResult();
        
        for (SqlAnalysisResult.QueryInfo queryInfo : sqlResult.getAllQueries()) {
            result.addQueryInfo(queryInfo);
        }
        
        return result;
    }
    
    /**
     * 打印按表组织的分析结果
     */
    public void printResults() {
        System.out.println("=== 按表组织的字段分析结果 ===");
        System.out.println("总共涉及 " + tableFieldMap.size() + " 个表");
        System.out.println();
        
        // 按表名排序输出
        List<String> sortedTables = new ArrayList<>(tableFieldMap.keySet());
        Collections.sort(sortedTables);
        
        for (String tableName : sortedTables) {
            TableFieldInfo tableInfo = tableFieldMap.get(tableName);
            System.out.println(tableInfo.toString());
        }
        
        System.out.println();
        System.out.println("=== 详细统计 ===");
        for (String tableName : sortedTables) {
            TableFieldInfo tableInfo = tableFieldMap.get(tableName);
            System.out.println(String.format("表 %s: 关联字段%d个, 过滤字段%d个, 总字段%d个", 
                             tableName, 
                             tableInfo.getJoinFields().size(),
                             tableInfo.getFilterFields().size(),
                             tableInfo.getAllFields().size()));
        }
    }
    
    /**
     * 获取所有表的字段信息
     */
    public Map<String, TableFieldInfo> getTableFieldMap() {
        return tableFieldMap;
    }
}
