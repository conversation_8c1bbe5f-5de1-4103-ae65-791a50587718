import java.util.HashSet;
import java.util.Set;

/**
 * 新的分组统计功能测试
 */
public class CsvOutputTest {
    public static void main(String[] args) {
        System.out.println("=== 表+过滤字段分组统计演示 ===");
        System.out.println("输出格式：表名：关联字段[...],过滤字段[...],所有字段[...],出现次数N");
        System.out.println();

        // 创建测试数据
        SqlAnalysisResult sqlResult = new SqlAnalysisResult();

        // 模拟一些测试数据
        createTestData(sqlResult);

        // 打印分析结果
        sqlResult.printResults();

        System.out.println("\n=== 新功能说明 ===");
        System.out.println("✅ 按表+过滤字段组合进行分组");
        System.out.println("✅ 相同组合的查询会累计次数");
        System.out.println("✅ 关联字段会自动合并");
        System.out.println("✅ 字段名自动清理（去掉表别名前缀）");
        System.out.println("✅ 支持递归处理子查询");
    }
    
    private static void createTestData(SqlAnalysisResult sqlResult) {
        // 用户表查询1
        Set<String> tables1 = new HashSet<>();
        tables1.add("USERS");

        Set<String> filterColumns1 = new HashSet<>();
        filterColumns1.add("u.status");

        Set<String> joinColumns1 = new HashSet<>();

        String sql1 = "SELECT u.id, u.name FROM users u WHERE u.status = 'active'";
        sqlResult.addPlainSelectResult(tables1, filterColumns1, joinColumns1, sql1);

        // 用户表查询2 - 相同过滤条件，会被聚合
        Set<String> tables2 = new HashSet<>();
        tables2.add("USERS");

        Set<String> filterColumns2 = new HashSet<>();
        filterColumns2.add("u.status");

        Set<String> joinColumns2 = new HashSet<>();

        String sql2 = "SELECT u.email FROM users u WHERE u.status = 'active'";
        sqlResult.addPlainSelectResult(tables2, filterColumns2, joinColumns2, sql2);

        // 订单表查询 - 包含关联字段
        Set<String> tables3 = new HashSet<>();
        tables3.add("ORDERS");
        tables3.add("USERS");

        Set<String> filterColumns3 = new HashSet<>();
        filterColumns3.add("o.status");

        Set<String> joinColumns3 = new HashSet<>();
        joinColumns3.add("o.user_id");
        joinColumns3.add("u.id");

        String sql3 = "SELECT o.id, u.name FROM orders o, users u WHERE o.user_id = u.id AND o.status = 'completed'";
        sqlResult.addPlainSelectResult(tables3, filterColumns3, joinColumns3, sql3);

        // 产品表查询 - 多个过滤条件
        Set<String> tables4 = new HashSet<>();
        tables4.add("PRODUCTS");

        Set<String> filterColumns4 = new HashSet<>();
        filterColumns4.add("category_id");
        filterColumns4.add("price");
        filterColumns4.add("active");

        Set<String> joinColumns4 = new HashSet<>();

        String sql4 = "SELECT * FROM products WHERE category_id = 1 AND price > 100 AND active = 1";
        sqlResult.addPlainSelectResult(tables4, filterColumns4, joinColumns4, sql4);

        System.out.println("已创建 4 个测试查询");
        System.out.println();
    }
}
